<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile App Module Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #c0392b;
        }
        #results {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>🧪 Mobile App Module Test</h1>
    <p>This page tests if all JavaScript modules are loading correctly.</p>

    <div class="test-section">
        <h2>Module Loading Tests</h2>
        <button onclick="runAllTests()">Run All Tests</button>
        <button onclick="clearResults()">Clear Results</button>
        <div id="results"></div>
    </div>

    <div class="test-section">
        <h2>API Connection Test</h2>
        <button onclick="testAPIConnection()">Test API</button>
        <div id="api-results"></div>
    </div>

    <div class="test-section">
        <h2>Feature Tests</h2>
        <button onclick="testProjectsModule()">Test Projects</button>
        <button onclick="testMediaModule()">Test Media</button>
        <button onclick="testContentModule()">Test Content</button>
        <div id="feature-results"></div>
    </div>

    <!-- Load the mobile app scripts -->
    <script src="js/app.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/notifications.js"></script>
    <script src="js/projects.js"></script>
    <script src="js/media.js"></script>
    <script src="js/content.js"></script>

    <script>
        function addResult(container, message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            document.getElementById(container).appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('api-results').innerHTML = '';
            document.getElementById('feature-results').innerHTML = '';
        }

        function runAllTests() {
            clearResults();
            
            // Test 1: Check if main app class exists
            if (typeof FloriAdmin !== 'undefined') {
                addResult('results', '✅ FloriAdmin class loaded successfully', 'pass');
            } else {
                addResult('results', '❌ FloriAdmin class not found', 'fail');
            }

            // Test 2: Check if auth manager exists
            if (typeof AuthManager !== 'undefined') {
                addResult('results', '✅ AuthManager class loaded successfully', 'pass');
            } else {
                addResult('results', '❌ AuthManager class not found', 'fail');
            }

            // Test 3: Check if notification manager exists
            if (typeof NotificationManager !== 'undefined') {
                addResult('results', '✅ NotificationManager class loaded successfully', 'pass');
            } else {
                addResult('results', '❌ NotificationManager class not found', 'fail');
            }

            // Test 4: Check if projects manager exists
            if (typeof ProjectsManager !== 'undefined') {
                addResult('results', '✅ ProjectsManager class loaded successfully', 'pass');
            } else {
                addResult('results', '❌ ProjectsManager class not found', 'fail');
            }

            // Test 5: Check if media manager exists
            if (typeof MediaManager !== 'undefined') {
                addResult('results', '✅ MediaManager class loaded successfully', 'pass');
            } else {
                addResult('results', '❌ MediaManager class not found', 'fail');
            }

            // Test 6: Check if content manager exists
            if (typeof ContentManager !== 'undefined') {
                addResult('results', '✅ ContentManager class loaded successfully', 'pass');
            } else {
                addResult('results', '❌ ContentManager class not found', 'fail');
            }

            // Test 7: Check global instances
            if (window.floriAdmin) {
                addResult('results', '✅ Global floriAdmin instance available', 'pass');
            } else {
                addResult('results', '❌ Global floriAdmin instance not found', 'fail');
            }

            if (window.authManager) {
                addResult('results', '✅ Global authManager instance available', 'pass');
            } else {
                addResult('results', '❌ Global authManager instance not found', 'fail');
            }

            if (window.notificationManager) {
                addResult('results', '✅ Global notificationManager instance available', 'pass');
            } else {
                addResult('results', '❌ Global notificationManager instance not found', 'fail');
            }

            if (window.ProjectsManager) {
                addResult('results', '✅ Global ProjectsManager instance available', 'pass');
            } else {
                addResult('results', '❌ Global ProjectsManager instance not found', 'fail');
            }

            if (window.MediaManager) {
                addResult('results', '✅ Global MediaManager instance available', 'pass');
            } else {
                addResult('results', '❌ Global MediaManager instance not found', 'fail');
            }

            if (window.ContentManager) {
                addResult('results', '✅ Global ContentManager instance available', 'pass');
            } else {
                addResult('results', '❌ Global ContentManager instance not found', 'fail');
            }

            // Test 8: Service Worker
            if ('serviceWorker' in navigator) {
                addResult('results', '✅ Service Worker supported', 'pass');
            } else {
                addResult('results', '❌ Service Worker not supported', 'fail');
            }

            // Test 9: PWA Features
            if ('PushManager' in window) {
                addResult('results', '✅ Push notifications supported', 'pass');
            } else {
                addResult('results', '❌ Push notifications not supported', 'fail');
            }

            addResult('results', '🏁 Module loading tests completed', 'info');
        }

        async function testAPIConnection() {
            try {
                addResult('api-results', '🔄 Testing API connection...', 'info');
                
                const response = await fetch('../api/auth.php?action=verify', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    addResult('api-results', '✅ API endpoint accessible', 'pass');
                } else {
                    addResult('api-results', `⚠️ API returned status: ${response.status}`, 'info');
                }

                // Test mobile API
                const mobileResponse = await fetch('../api/mobile.php?action=dashboard');
                if (mobileResponse.ok) {
                    addResult('api-results', '✅ Mobile API endpoint accessible', 'pass');
                } else {
                    addResult('api-results', `❌ Mobile API error: ${mobileResponse.status}`, 'fail');
                }

            } catch (error) {
                addResult('api-results', `❌ API connection failed: ${error.message}`, 'fail');
            }
        }

        function testProjectsModule() {
            if (window.ProjectsManager) {
                addResult('feature-results', '✅ Projects module ready', 'pass');
                
                // Test if methods exist
                const methods = ['load', 'showAddProjectModal', 'handleCreateProject'];
                methods.forEach(method => {
                    if (typeof window.ProjectsManager[method] === 'function') {
                        addResult('feature-results', `✅ ProjectsManager.${method}() available`, 'pass');
                    } else {
                        addResult('feature-results', `❌ ProjectsManager.${method}() missing`, 'fail');
                    }
                });
            } else {
                addResult('feature-results', '❌ Projects module not loaded', 'fail');
            }
        }

        function testMediaModule() {
            if (window.MediaManager) {
                addResult('feature-results', '✅ Media module ready', 'pass');
                
                // Test if methods exist
                const methods = ['load', 'showUploadModal', 'handleFileSelection'];
                methods.forEach(method => {
                    if (typeof window.MediaManager[method] === 'function') {
                        addResult('feature-results', `✅ MediaManager.${method}() available`, 'pass');
                    } else {
                        addResult('feature-results', `❌ MediaManager.${method}() missing`, 'fail');
                    }
                });
            } else {
                addResult('feature-results', '❌ Media module not loaded', 'fail');
            }
        }

        function testContentModule() {
            if (window.ContentManager) {
                addResult('feature-results', '✅ Content module ready', 'pass');
                
                // Test if methods exist
                const methods = ['load', 'renderContentSections', 'handleContentUpdate'];
                methods.forEach(method => {
                    if (typeof window.ContentManager[method] === 'function') {
                        addResult('feature-results', `✅ ContentManager.${method}() available`, 'pass');
                    } else {
                        addResult('feature-results', `❌ ContentManager.${method}() missing`, 'fail');
                    }
                });
            } else {
                addResult('feature-results', '❌ Content module not loaded', 'fail');
            }
        }

        // Auto-run tests when page loads
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                addResult('results', '🚀 Starting automatic module tests...', 'info');
                runAllTests();
            }, 1000);
        });
    </script>
</body>
</html>
