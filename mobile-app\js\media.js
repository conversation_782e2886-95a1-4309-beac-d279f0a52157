/**
 * Media Manager for Flori Construction Mobile App
 * Handles media upload, management, and gallery functionality
 */

class MediaManager {
    constructor() {
        this.apiBase = '../api';
        this.currentPage = 1;
        this.itemsPerPage = 20;
        this.currentFilter = '';
        this.media = [];
        this.selectedFiles = [];
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // Upload media button
        const uploadBtn = document.getElementById('upload-media-btn');
        if (uploadBtn) {
            uploadBtn.addEventListener('click', () => {
                this.showUploadModal();
            });
        }
        
        // Media type filter
        const filterSelect = document.getElementById('media-type-filter');
        if (filterSelect) {
            filterSelect.addEventListener('change', (e) => {
                this.currentFilter = e.target.value;
                this.currentPage = 1;
                this.load();
            });
        }
        
        // File input change
        const fileInput = document.getElementById('file-input');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                this.handleFileSelection(e.target.files);
            });
        }
    }
    
    async load() {
        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.itemsPerPage
            });
            
            if (this.currentFilter) {
                params.append('type', this.currentFilter);
            }
            
            const response = await window.floriAdmin.apiRequest(`media.php?${params}`);
            
            if (response && response.success) {
                this.media = response.media;
                this.renderMedia();
                this.renderPagination(response.pagination);
            } else {
                throw new Error(response?.error || 'Failed to load media');
            }
            
        } catch (error) {
            console.error('Failed to load media:', error);
            window.floriAdmin.showToast('Failed to load media', 'error');
        }
    }
    
    renderMedia() {
        const container = document.getElementById('media-grid');
        if (!container) return;
        
        if (this.media.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-images fa-3x"></i>
                    <h3>No media files found</h3>
                    <p>Upload your first media file</p>
                    <button class="btn btn-primary" onclick="window.MediaManager.showUploadModal()">
                        <i class="fas fa-upload"></i> Upload Media
                    </button>
                </div>
            `;
            return;
        }
        
        const html = this.media.map(item => `
            <div class="media-card" data-id="${item.id}">
                <div class="media-preview">
                    ${this.renderMediaPreview(item)}
                    <div class="media-overlay">
                        <button class="btn btn-ghost btn-sm" onclick="window.MediaManager.viewMedia(${item.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-ghost btn-sm" onclick="window.MediaManager.deleteMedia(${item.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="media-info">
                    <h4 class="media-title">${window.floriAdmin.escapeHtml(item.original_name)}</h4>
                    <p class="media-meta">
                        <span class="media-type">${item.file_type.toUpperCase()}</span>
                        <span class="media-size">${window.floriAdmin.formatFileSize(item.file_size)}</span>
                    </p>
                    <p class="media-date">${window.floriAdmin.formatDate(item.created_at)}</p>
                </div>
            </div>
        `).join('');
        
        container.innerHTML = html;
    }
    
    renderMediaPreview(item) {
        const baseUrl = '../uploads/';
        const filePath = baseUrl + item.file_path;
        
        if (item.file_type.startsWith('image/')) {
            return `<img src="${filePath}" alt="${window.floriAdmin.escapeHtml(item.original_name)}" loading="lazy">`;
        } else if (item.file_type.startsWith('video/')) {
            return `
                <video preload="metadata">
                    <source src="${filePath}" type="${item.file_type}">
                </video>
                <div class="video-overlay">
                    <i class="fas fa-play"></i>
                </div>
            `;
        } else {
            return `
                <div class="file-preview">
                    <i class="fas fa-file fa-3x"></i>
                    <span class="file-extension">${this.getFileExtension(item.original_name)}</span>
                </div>
            `;
        }
    }
    
    renderPagination(pagination) {
        const container = document.getElementById('media-pagination');
        if (!container || !pagination) return;
        
        const { page, pages, total } = pagination;
        
        if (pages <= 1) {
            container.innerHTML = '';
            return;
        }
        
        let html = `
            <button class="pagination-btn" ${page <= 1 ? 'disabled' : ''} 
                    onclick="window.MediaManager.goToPage(${page - 1})">
                <i class="fas fa-chevron-left"></i>
            </button>
        `;
        
        // Page numbers
        const startPage = Math.max(1, page - 2);
        const endPage = Math.min(pages, page + 2);
        
        for (let i = startPage; i <= endPage; i++) {
            html += `
                <button class="pagination-btn ${i === page ? 'active' : ''}" 
                        onclick="window.MediaManager.goToPage(${i})">
                    ${i}
                </button>
            `;
        }
        
        html += `
            <button class="pagination-btn" ${page >= pages ? 'disabled' : ''} 
                    onclick="window.MediaManager.goToPage(${page + 1})">
                <i class="fas fa-chevron-right"></i>
            </button>
        `;
        
        container.innerHTML = html;
    }
    
    goToPage(page) {
        this.currentPage = page;
        this.load();
    }
    
    showUploadModal() {
        const modalContent = `
            <div class="modal-header">
                <h2>Upload Media</h2>
                <button class="modal-close" onclick="window.floriAdmin.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="upload-area" id="upload-area">
                <div class="upload-content">
                    <i class="fas fa-cloud-upload-alt fa-3x"></i>
                    <h3>Drop files here or click to browse</h3>
                    <p>Supports images, videos, and documents</p>
                    <button type="button" class="btn btn-primary" onclick="document.getElementById('file-input').click()">
                        <i class="fas fa-folder-open"></i> Browse Files
                    </button>
                </div>
            </div>
            <div id="selected-files" class="selected-files"></div>
            <div class="upload-options">
                <div class="form-group">
                    <label for="upload-directory">Directory</label>
                    <select id="upload-directory">
                        <option value="general">General</option>
                        <option value="projects">Projects</option>
                        <option value="gallery">Gallery</option>
                        <option value="documents">Documents</option>
                    </select>
                </div>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-ghost" onclick="window.floriAdmin.closeModal()">
                    Cancel
                </button>
                <button type="button" class="btn btn-primary" id="upload-files-btn" disabled>
                    <i class="fas fa-upload"></i> Upload Files
                </button>
            </div>
        `;
        
        window.floriAdmin.showModal(modalContent);
        
        // Setup drag and drop
        this.setupDragAndDrop();
        
        // Setup upload button
        const uploadBtn = document.getElementById('upload-files-btn');
        if (uploadBtn) {
            uploadBtn.addEventListener('click', () => {
                this.uploadSelectedFiles();
            });
        }
    }
    
    setupDragAndDrop() {
        const uploadArea = document.getElementById('upload-area');
        if (!uploadArea) return;
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('drag-over');
        });
        
        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('drag-over');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('drag-over');
            this.handleFileSelection(e.dataTransfer.files);
        });
        
        uploadArea.addEventListener('click', () => {
            document.getElementById('file-input').click();
        });
    }
    
    handleFileSelection(files) {
        this.selectedFiles = Array.from(files);
        this.renderSelectedFiles();
        
        const uploadBtn = document.getElementById('upload-files-btn');
        if (uploadBtn) {
            uploadBtn.disabled = this.selectedFiles.length === 0;
        }
    }
    
    renderSelectedFiles() {
        const container = document.getElementById('selected-files');
        if (!container) return;
        
        if (this.selectedFiles.length === 0) {
            container.innerHTML = '';
            return;
        }
        
        const html = this.selectedFiles.map((file, index) => `
            <div class="selected-file">
                <div class="file-info">
                    <i class="fas fa-file"></i>
                    <span class="file-name">${window.floriAdmin.escapeHtml(file.name)}</span>
                    <span class="file-size">${window.floriAdmin.formatFileSize(file.size)}</span>
                </div>
                <button type="button" class="btn btn-ghost btn-sm" onclick="window.MediaManager.removeSelectedFile(${index})">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `).join('');
        
        container.innerHTML = `
            <h4>Selected Files (${this.selectedFiles.length})</h4>
            ${html}
        `;
    }
    
    removeSelectedFile(index) {
        this.selectedFiles.splice(index, 1);
        this.renderSelectedFiles();
        
        const uploadBtn = document.getElementById('upload-files-btn');
        if (uploadBtn) {
            uploadBtn.disabled = this.selectedFiles.length === 0;
        }
    }
    
    async uploadSelectedFiles() {
        if (this.selectedFiles.length === 0) return;
        
        const uploadBtn = document.getElementById('upload-files-btn');
        const directory = document.getElementById('upload-directory').value;
        
        // Show loading state
        const originalText = uploadBtn.innerHTML;
        uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';
        uploadBtn.disabled = true;
        
        try {
            const uploadPromises = this.selectedFiles.map(file => this.uploadSingleFile(file, directory));
            const results = await Promise.allSettled(uploadPromises);
            
            const successful = results.filter(r => r.status === 'fulfilled').length;
            const failed = results.filter(r => r.status === 'rejected').length;
            
            if (successful > 0) {
                window.floriAdmin.showToast(`${successful} file(s) uploaded successfully!`, 'success');
                this.load(); // Reload media list
            }
            
            if (failed > 0) {
                window.floriAdmin.showToast(`${failed} file(s) failed to upload`, 'warning');
            }
            
            window.floriAdmin.closeModal();
            
        } catch (error) {
            console.error('Upload failed:', error);
            window.floriAdmin.showToast('Upload failed: ' + error.message, 'error');
        } finally {
            // Restore button state
            uploadBtn.innerHTML = originalText;
            uploadBtn.disabled = false;
        }
    }
    
    async uploadSingleFile(file, directory) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('directory', directory);
        
        const response = await fetch(`${this.apiBase}/media.php`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${window.floriAdmin.token}`
            },
            body: formData
        });
        
        const result = await response.json();
        
        if (!result.success) {
            throw new Error(result.error || 'Upload failed');
        }
        
        return result;
    }
    
    viewMedia(mediaId) {
        const media = this.media.find(m => m.id === mediaId);
        if (!media) return;
        
        const baseUrl = '../uploads/';
        const filePath = baseUrl + media.file_path;
        
        let content = '';
        
        if (media.file_type.startsWith('image/')) {
            content = `<img src="${filePath}" alt="${window.floriAdmin.escapeHtml(media.original_name)}" style="max-width: 100%; height: auto;">`;
        } else if (media.file_type.startsWith('video/')) {
            content = `
                <video controls style="max-width: 100%; height: auto;">
                    <source src="${filePath}" type="${media.file_type}">
                    Your browser does not support the video tag.
                </video>
            `;
        } else {
            content = `
                <div class="file-preview-large">
                    <i class="fas fa-file fa-5x"></i>
                    <h3>${window.floriAdmin.escapeHtml(media.original_name)}</h3>
                    <p>File Type: ${media.file_type}</p>
                    <p>Size: ${window.floriAdmin.formatFileSize(media.file_size)}</p>
                    <a href="${filePath}" target="_blank" class="btn btn-primary">
                        <i class="fas fa-download"></i> Download
                    </a>
                </div>
            `;
        }
        
        const modalContent = `
            <div class="modal-header">
                <h2>${window.floriAdmin.escapeHtml(media.original_name)}</h2>
                <button class="modal-close" onclick="window.floriAdmin.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="media-viewer">
                ${content}
            </div>
        `;
        
        window.floriAdmin.showModal(modalContent);
    }
    
    async deleteMedia(mediaId) {
        if (!confirm('Are you sure you want to delete this media file?')) {
            return;
        }
        
        try {
            const response = await window.floriAdmin.apiRequest(`media.php?id=${mediaId}`, {
                method: 'DELETE'
            });
            
            if (response && response.success) {
                window.floriAdmin.showToast('Media deleted successfully!', 'success');
                this.load(); // Reload media list
            } else {
                throw new Error(response?.error || 'Failed to delete media');
            }
            
        } catch (error) {
            console.error('Failed to delete media:', error);
            window.floriAdmin.showToast('Failed to delete media: ' + error.message, 'error');
        }
    }
    
    getFileExtension(filename) {
        return filename.split('.').pop().toUpperCase();
    }
}

// Initialize media manager
document.addEventListener('DOMContentLoaded', () => {
    window.MediaManager = new MediaManager();
});
