/**
 * Projects Manager for Flori Construction Mobile App
 * Handles project listing, creation, editing, and management
 */

class ProjectsManager {
    constructor() {
        this.apiBase = '../api';
        this.currentPage = 1;
        this.itemsPerPage = 12;
        this.currentFilter = '';
        this.currentSearch = '';
        this.projects = [];
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // Add project button
        const addProjectBtn = document.getElementById('add-project-btn');
        if (addProjectBtn) {
            addProjectBtn.addEventListener('click', () => {
                this.showAddProjectModal();
            });
        }
        
        // Filter dropdown
        const filterSelect = document.getElementById('project-type-filter');
        if (filterSelect) {
            filterSelect.addEventListener('change', (e) => {
                this.currentFilter = e.target.value;
                this.currentPage = 1;
                this.load();
            });
        }
        
        // Search input
        const searchInput = document.getElementById('project-search');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.currentSearch = e.target.value;
                    this.currentPage = 1;
                    this.load();
                }, 500);
            });
        }
    }
    
    async load() {
        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.itemsPerPage
            });
            
            if (this.currentFilter) {
                params.append('type', this.currentFilter);
            }
            
            if (this.currentSearch) {
                params.append('search', this.currentSearch);
            }
            
            const response = await window.floriAdmin.apiRequest(`projects.php?${params}`);
            
            if (response && response.success) {
                this.projects = response.projects;
                this.renderProjects();
                this.renderPagination(response.pagination);
            } else {
                throw new Error(response?.error || 'Failed to load projects');
            }
            
        } catch (error) {
            console.error('Failed to load projects:', error);
            window.floriAdmin.showToast('Failed to load projects', 'error');
        }
    }
    
    renderProjects() {
        const container = document.getElementById('projects-list');
        if (!container) return;
        
        if (this.projects.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-building fa-3x"></i>
                    <h3>No projects found</h3>
                    <p>Start by adding your first project</p>
                    <button class="btn btn-primary" onclick="window.ProjectsManager.showAddProjectModal()">
                        <i class="fas fa-plus"></i> Add Project
                    </button>
                </div>
            `;
            return;
        }
        
        const html = this.projects.map(project => `
            <div class="project-card" data-id="${project.id}">
                <div class="project-image" style="background-image: url('${this.getProjectImage(project)}')">
                    <div class="project-actions">
                        <button class="btn btn-ghost btn-sm" onclick="window.ProjectsManager.editProject(${project.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-ghost btn-sm" onclick="window.ProjectsManager.deleteProject(${project.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="project-content">
                    <h3 class="project-title">${window.floriAdmin.escapeHtml(project.title)}</h3>
                    <p class="project-meta">
                        <i class="fas fa-map-marker-alt"></i> ${window.floriAdmin.escapeHtml(project.location || 'No location')}
                    </p>
                    <p class="project-description">${this.truncateText(project.short_description || project.description, 100)}</p>
                    <div class="project-footer">
                        <span class="project-type ${project.project_type}">${project.project_type}</span>
                        <span class="project-date">${window.floriAdmin.formatDate(project.created_at)}</span>
                    </div>
                </div>
            </div>
        `).join('');
        
        container.innerHTML = html;
    }
    
    renderPagination(pagination) {
        const container = document.getElementById('projects-pagination');
        if (!container || !pagination) return;
        
        const { page, pages, total } = pagination;
        
        if (pages <= 1) {
            container.innerHTML = '';
            return;
        }
        
        let html = `
            <button class="pagination-btn" ${page <= 1 ? 'disabled' : ''} 
                    onclick="window.ProjectsManager.goToPage(${page - 1})">
                <i class="fas fa-chevron-left"></i>
            </button>
        `;
        
        // Page numbers
        const startPage = Math.max(1, page - 2);
        const endPage = Math.min(pages, page + 2);
        
        for (let i = startPage; i <= endPage; i++) {
            html += `
                <button class="pagination-btn ${i === page ? 'active' : ''}" 
                        onclick="window.ProjectsManager.goToPage(${i})">
                    ${i}
                </button>
            `;
        }
        
        html += `
            <button class="pagination-btn" ${page >= pages ? 'disabled' : ''} 
                    onclick="window.ProjectsManager.goToPage(${page + 1})">
                <i class="fas fa-chevron-right"></i>
            </button>
        `;
        
        container.innerHTML = html;
    }
    
    goToPage(page) {
        this.currentPage = page;
        this.load();
    }
    
    getProjectImage(project) {
        if (project.featured_image) {
            return `../uploads/${project.featured_image}`;
        }
        return '../assets/images/placeholder-project.jpg';
    }
    
    truncateText(text, maxLength) {
        if (!text) return '';
        if (text.length <= maxLength) return window.floriAdmin.escapeHtml(text);
        return window.floriAdmin.escapeHtml(text.substring(0, maxLength)) + '...';
    }
    
    showAddProjectModal() {
        const modalContent = `
            <div class="modal-header">
                <h2>Add New Project</h2>
                <button class="modal-close" onclick="window.floriAdmin.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="add-project-form" class="project-form">
                <div class="form-group">
                    <label for="project-title">Project Title *</label>
                    <input type="text" id="project-title" name="title" required>
                </div>
                
                <div class="form-group">
                    <label for="project-type">Project Type *</label>
                    <select id="project-type" name="project_type" required>
                        <option value="">Select Type</option>
                        <option value="completed">Completed</option>
                        <option value="ongoing">Ongoing</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="project-location">Location</label>
                    <input type="text" id="project-location" name="location">
                </div>
                
                <div class="form-group">
                    <label for="project-client">Client Name</label>
                    <input type="text" id="project-client" name="client_name">
                </div>
                
                <div class="form-group">
                    <label for="project-short-desc">Short Description</label>
                    <textarea id="project-short-desc" name="short_description" rows="3"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="project-description">Full Description</label>
                    <textarea id="project-description" name="description" rows="5"></textarea>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" name="is_featured"> Featured Project
                    </label>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-ghost" onclick="window.floriAdmin.closeModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Project
                    </button>
                </div>
            </form>
        `;
        
        window.floriAdmin.showModal(modalContent);
        
        // Setup form submission
        const form = document.getElementById('add-project-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleCreateProject(form);
            });
        }
    }
    
    async handleCreateProject(form) {
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        
        // Show loading state
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
        submitBtn.disabled = true;
        
        try {
            const projectData = {
                title: formData.get('title'),
                project_type: formData.get('project_type'),
                location: formData.get('location'),
                client_name: formData.get('client_name'),
                short_description: formData.get('short_description'),
                description: formData.get('description'),
                is_featured: formData.has('is_featured')
            };
            
            const response = await window.floriAdmin.apiRequest('projects.php', {
                method: 'POST',
                body: JSON.stringify(projectData)
            });
            
            if (response && response.success) {
                window.floriAdmin.showToast('Project created successfully!', 'success');
                window.floriAdmin.closeModal();
                this.load(); // Reload projects list
            } else {
                throw new Error(response?.error || 'Failed to create project');
            }
            
        } catch (error) {
            console.error('Failed to create project:', error);
            window.floriAdmin.showToast('Failed to create project: ' + error.message, 'error');
        } finally {
            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }
    
    async editProject(projectId) {
        // Implementation for editing projects
        window.floriAdmin.showToast('Edit project feature coming soon', 'info');
    }
    
    async deleteProject(projectId) {
        if (!confirm('Are you sure you want to delete this project?')) {
            return;
        }
        
        try {
            const response = await window.floriAdmin.apiRequest(`projects.php?id=${projectId}`, {
                method: 'DELETE'
            });
            
            if (response && response.success) {
                window.floriAdmin.showToast('Project deleted successfully!', 'success');
                this.load(); // Reload projects list
            } else {
                throw new Error(response?.error || 'Failed to delete project');
            }
            
        } catch (error) {
            console.error('Failed to delete project:', error);
            window.floriAdmin.showToast('Failed to delete project: ' + error.message, 'error');
        }
    }
}

// Initialize projects manager
document.addEventListener('DOMContentLoaded', () => {
    window.ProjectsManager = new ProjectsManager();
});
