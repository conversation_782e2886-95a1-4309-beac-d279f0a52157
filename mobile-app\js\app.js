/**
 * Main App JavaScript for Flori Construction Admin
 * Handles app initialization, navigation, and core functionality
 */

class FloriAdmin {
    constructor() {
        this.apiBase = '../api';
        this.token = localStorage.getItem('flori_token');
        this.user = null;
        this.currentPage = 'dashboard';
        
        this.init();
    }
    
    async init() {
        // Show loading screen
        this.showLoading();
        
        // Check if user is logged in
        if (this.token) {
            const isValid = await this.verifyToken();
            if (isValid) {
                this.showMainApp();
                this.loadDashboard();
            } else {
                this.showLogin();
            }
        } else {
            this.showLogin();
        }
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Register service worker
        this.registerServiceWorker();
        
        // Hide loading screen
        this.hideLoading();
    }
    
    setupEventListeners() {
        // Menu toggle for mobile
        const menuToggle = document.getElementById('menu-toggle');
        const sidebar = document.getElementById('sidebar');
        
        menuToggle?.addEventListener('click', () => {
            sidebar.classList.toggle('active');
        });
        
        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', (e) => {
            if (window.innerWidth <= 768 && 
                !sidebar.contains(e.target) && 
                !menuToggle.contains(e.target)) {
                sidebar.classList.remove('active');
            }
        });
        
        // Navigation menu items
        const menuItems = document.querySelectorAll('.menu-item');
        menuItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const page = item.dataset.page;
                this.navigateTo(page);
                
                // Close mobile menu
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('active');
                }
            });
        });
        
        // Logout button
        const logoutBtn = document.getElementById('logout-btn');
        logoutBtn?.addEventListener('click', () => {
            this.logout();
        });
        
        // Modal close
        const modalOverlay = document.getElementById('modal-overlay');
        modalOverlay?.addEventListener('click', (e) => {
            if (e.target === modalOverlay) {
                this.closeModal();
            }
        });
        
        // Handle back button
        window.addEventListener('popstate', (e) => {
            const page = e.state?.page || 'dashboard';
            this.navigateTo(page, false);
        });
    }
    
    async verifyToken() {
        try {
            const response = await fetch(`${this.apiBase}/auth.php?action=verify`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.user = data.user;
                return true;
            }
            
            return false;
        } catch (error) {
            console.error('Token verification failed:', error);
            return false;
        }
    }
    
    navigateTo(page, pushState = true) {
        // Update active menu item
        document.querySelectorAll('.menu-item').forEach(item => {
            item.classList.remove('active');
        });
        
        const activeMenuItem = document.querySelector(`[data-page="${page}"]`);
        activeMenuItem?.classList.add('active');
        
        // Hide all pages
        document.querySelectorAll('.page').forEach(p => {
            p.classList.remove('active');
        });
        
        // Show target page
        const targetPage = document.getElementById(`${page}-page`);
        if (targetPage) {
            targetPage.classList.add('active');
            this.currentPage = page;
            
            // Update page title
            const pageTitle = document.getElementById('page-title');
            if (pageTitle) {
                pageTitle.textContent = this.getPageTitle(page);
            }
            
            // Load page content
            this.loadPageContent(page);
            
            // Update URL
            if (pushState) {
                history.pushState({ page }, '', `#${page}`);
            }
        }
    }
    
    getPageTitle(page) {
        const titles = {
            dashboard: 'Dashboard',
            projects: 'Projects',
            media: 'Media',
            content: 'Content',
            settings: 'Settings'
        };
        
        return titles[page] || 'Dashboard';
    }
    
    async loadPageContent(page) {
        switch (page) {
            case 'dashboard':
                await this.loadDashboard();
                break;
            case 'projects':
                await this.loadProjects();
                break;
            case 'media':
                await this.loadMedia();
                break;
            case 'content':
                await this.loadContent();
                break;
            case 'settings':
                await this.loadSettings();
                break;
        }
    }
    
    async loadDashboard() {
        try {
            // Load dashboard statistics
            const [projectsResponse, mediaResponse] = await Promise.all([
                fetch(`${this.apiBase}/projects.php`, {
                    headers: { 'Authorization': `Bearer ${this.token}` }
                }),
                fetch(`${this.apiBase}/media.php`, {
                    headers: { 'Authorization': `Bearer ${this.token}` }
                })
            ]);
            
            const projectsData = await projectsResponse.json();
            const mediaData = await mediaResponse.json();
            
            if (projectsData.success) {
                const projects = projectsData.projects;
                const completed = projects.filter(p => p.project_type === 'completed').length;
                const ongoing = projects.filter(p => p.project_type === 'ongoing').length;
                
                document.getElementById('total-projects').textContent = projects.length;
                document.getElementById('completed-projects').textContent = completed;
                document.getElementById('ongoing-projects').textContent = ongoing;
                
                // Load recent projects
                this.loadRecentProjects(projects.slice(0, 5));
            }
            
            if (mediaData.success) {
                document.getElementById('total-media').textContent = mediaData.pagination.total;
            }
            
        } catch (error) {
            console.error('Failed to load dashboard:', error);
            this.showToast('Failed to load dashboard data', 'error');
        }
    }
    
    loadRecentProjects(projects) {
        const container = document.getElementById('recent-projects');
        if (!container) return;
        
        if (projects.length === 0) {
            container.innerHTML = '<p>No recent projects</p>';
            return;
        }
        
        const html = projects.map(project => `
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="fas fa-building"></i>
                </div>
                <div class="activity-content">
                    <h4>${this.escapeHtml(project.title)}</h4>
                    <p>${this.escapeHtml(project.location || 'No location specified')}</p>
                    <span class="activity-time">${this.formatDate(project.created_at)}</span>
                </div>
                <span class="project-type ${project.project_type}">${project.project_type}</span>
            </div>
        `).join('');
        
        container.innerHTML = html;
    }
    
    async loadProjects() {
        // This will be implemented in projects.js
        if (window.ProjectsManager) {
            window.ProjectsManager.load();
        }
    }
    
    async loadMedia() {
        // This will be implemented in media.js
        if (window.MediaManager) {
            window.MediaManager.load();
        }
    }
    
    async loadContent() {
        // This will be implemented in content.js
        if (window.ContentManager) {
            window.ContentManager.load();
        }
    }
    
    async loadSettings() {
        if (!this.user) return;
        
        // Populate user profile form
        const fullNameInput = document.getElementById('full-name');
        const emailInput = document.getElementById('email');
        
        if (fullNameInput) fullNameInput.value = this.user.full_name || '';
        if (emailInput) emailInput.value = this.user.email || '';
    }
    
    showLoading() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.style.display = 'flex';
        }
    }
    
    hideLoading() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.style.display = 'none';
        }
    }
    
    showLogin() {
        document.getElementById('login-screen').style.display = 'block';
        document.getElementById('main-app').style.display = 'none';
    }
    
    showMainApp() {
        document.getElementById('login-screen').style.display = 'none';
        document.getElementById('main-app').style.display = 'grid';
    }
    
    logout() {
        // Clear token and user data
        localStorage.removeItem('flori_token');
        this.token = null;
        this.user = null;
        
        // Call logout API
        fetch(`${this.apiBase}/auth.php?action=logout`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.token}`
            }
        }).catch(() => {
            // Ignore errors on logout
        });
        
        // Show login screen
        this.showLogin();
        
        // Show toast
        this.showToast('Logged out successfully', 'success');
    }
    
    showModal(content) {
        const modalOverlay = document.getElementById('modal-overlay');
        const modalContent = document.getElementById('modal-content');
        
        if (modalContent) {
            modalContent.innerHTML = content;
        }
        
        if (modalOverlay) {
            modalOverlay.classList.add('active');
        }
    }
    
    closeModal() {
        const modalOverlay = document.getElementById('modal-overlay');
        if (modalOverlay) {
            modalOverlay.classList.remove('active');
        }
    }
    
    showToast(message, type = 'info') {
        const container = document.getElementById('toast-container');
        if (!container) return;
        
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <span>${this.escapeHtml(message)}</span>
                <button class="toast-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        container.appendChild(toast);
        
        // Show toast
        setTimeout(() => toast.classList.add('show'), 100);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 5000);
    }
    
    async apiRequest(endpoint, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.token}`
            }
        };
        
        const mergedOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };
        
        try {
            const response = await fetch(`${this.apiBase}/${endpoint}`, mergedOptions);
            const data = await response.json();
            
            if (response.status === 401) {
                // Token expired or invalid
                this.logout();
                return null;
            }
            
            return data;
        } catch (error) {
            console.error('API request failed:', error);
            this.showToast('Network error occurred', 'error');
            return null;
        }
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-GB', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                await navigator.serviceWorker.register('/mobile-app/sw.js');
                console.log('Service Worker registered successfully');
            } catch (error) {
                console.log('Service Worker registration failed:', error);
            }
        }
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.floriAdmin = new FloriAdmin();
});

// Handle install prompt for PWA
let deferredPrompt;

window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault();
    deferredPrompt = e;
    
    // Show install button or banner
    const installBanner = document.createElement('div');
    installBanner.className = 'install-banner';
    installBanner.innerHTML = `
        <div class="install-content">
            <span>Install Flori Admin app for better experience</span>
            <button id="install-btn" class="btn btn-primary">Install</button>
            <button id="dismiss-install" class="btn btn-ghost">Dismiss</button>
        </div>
    `;
    
    document.body.appendChild(installBanner);
    
    document.getElementById('install-btn').addEventListener('click', async () => {
        if (deferredPrompt) {
            deferredPrompt.prompt();
            const { outcome } = await deferredPrompt.userChoice;
            console.log(`User response to the install prompt: ${outcome}`);
            deferredPrompt = null;
            installBanner.remove();
        }
    });
    
    document.getElementById('dismiss-install').addEventListener('click', () => {
        installBanner.remove();
    });
});

// Handle app installed
window.addEventListener('appinstalled', () => {
    console.log('PWA was installed');
    deferredPrompt = null;
});
